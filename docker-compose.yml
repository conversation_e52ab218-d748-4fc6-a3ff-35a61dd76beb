version: '3.8'

services:
  ip-locator:
    build: .
    container_name: ip-locator
    ports:
      - "8080:8080"
    volumes:
      - ip_data:/app/data
      - ip_backup:/app/backup
    environment:
      - ACCESS_LOG=off
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/check"]
      interval: 30s
      timeout: 10s
      retries: 3

  database-updater:
    image: curlimages/curl:latest
    container_name: ip-database-updater
    volumes:
      - ip_data:/app/data
      - ./scripts:/scripts:ro
    environment:
      - IPIP_TOKEN=${IPIP_TOKEN}
      - MAXMIND_LICENSE_KEY=${MAXMIND_LICENSE_KEY}
      - SERVICE_URL=http://ip-locator:8080
    command: >
      sh -c "
        # 等待主服务启动
        sleep 30
        while true; do
          echo 'Starting database update...'
          /scripts/update_databases.sh
          echo 'Sleeping for 24 hours...'
          sleep 86400
        done
      "
    depends_on:
      - ip-locator
    restart: unless-stopped

  # 可选：添加监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: ip-locator-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: ip-locator-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  ip_data:
    driver: local
  ip_backup:
    driver: local
  grafana_data:
    driver: local
