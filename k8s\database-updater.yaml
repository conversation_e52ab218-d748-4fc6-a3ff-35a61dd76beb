apiVersion: v1
kind: ConfigMap
metadata:
  name: db-updater-config
  namespace: default
data:
  update.sh: |
    #!/bin/bash
    set -e
    
    # 配置
    IPIP_TOKEN="${IPIP_TOKEN}"
    MAXMIND_LICENSE_KEY="${MAXMIND_LICENSE_KEY}"
    SERVICE_URL="${SERVICE_URL:-http://ip-locator-service:8080}"
    
    log() {
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    }
    
    # 下载并更新数据库
    update_database() {
        local db_type="$1"
        local download_url="$2"
        local filename="$3"
        
        log "Updating $db_type database..."
        
        # 下载到临时文件
        local temp_file="/tmp/$filename"
        
        if curl -L -o "$temp_file" "$download_url"; then
            # 上传到服务
            if curl -X POST \
                   -H "Content-Type: application/octet-stream" \
                   --data-binary "@$temp_file" \
                   "$SERVICE_URL/admin/upload?db=$db_type"; then
                log "$db_type database updated successfully"
                rm -f "$temp_file"
                return 0
            else
                log "Failed to upload $db_type database"
                rm -f "$temp_file"
                return 1
            fi
        else
            log "Failed to download $db_type database"
            return 1
        fi
    }
    
    # 更新 GeoLite2
    if [ -n "$MAXMIND_LICENSE_KEY" ]; then
        GEOLITE2_URL="https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=$MAXMIND_LICENSE_KEY&suffix=tar.gz"
        
        # 下载并解压
        curl -o /tmp/geolite2.tar.gz "$GEOLITE2_URL"
        cd /tmp
        tar -xzf geolite2.tar.gz
        
        # 找到 mmdb 文件并上传
        MMDB_FILE=$(find . -name "*.mmdb" -type f | head -1)
        if [ -n "$MMDB_FILE" ]; then
            curl -X POST \
                 -H "Content-Type: application/octet-stream" \
                 --data-binary "@$MMDB_FILE" \
                 "$SERVICE_URL/admin/upload?db=geolite2"
            log "GeoLite2 database updated"
        fi
        
        rm -rf /tmp/GeoLite2-* /tmp/geolite2.tar.gz
    fi
    
    # 更新纯真数据库
    CZ_URL="https://www.cz88.net/geo-public"
    curl -L -o /tmp/cz.zip "$CZ_URL"
    cd /tmp
    unzip -o cz.zip
    
    DAT_FILE=$(find . -name "*.dat" -type f | head -1)
    if [ -n "$DAT_FILE" ]; then
        curl -X POST \
             -H "Content-Type: application/octet-stream" \
             --data-binary "@$DAT_FILE" \
             "$SERVICE_URL/admin/upload?db=cz"
        log "CZ database updated"
    fi
    
    rm -f /tmp/cz.zip /tmp/*.dat
    
    log "Database update completed"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: ip-database-updater
  namespace: default
spec:
  # 每天凌晨 2 点执行
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: updater
            image: curlimages/curl:latest
            command:
            - /bin/sh
            - /scripts/update.sh
            env:
            - name: IPIP_TOKEN
              valueFrom:
                secretKeyRef:
                  name: ip-database-secrets
                  key: ipip-token
                  optional: true
            - name: MAXMIND_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  name: ip-database-secrets
                  key: maxmind-license-key
                  optional: true
            - name: SERVICE_URL
              value: "http://ip-locator-service:8080"
            volumeMounts:
            - name: update-script
              mountPath: /scripts
          volumes:
          - name: update-script
            configMap:
              name: db-updater-config
              defaultMode: 0755
          restartPolicy: OnFailure

---
apiVersion: v1
kind: Secret
metadata:
  name: ip-database-secrets
  namespace: default
type: Opaque
data:
  # Base64 编码的密钥，需要替换为实际值
  ipip-token: ""  # echo -n "your-ipip-token" | base64
  maxmind-license-key: ""  # echo -n "your-maxmind-key" | base64
