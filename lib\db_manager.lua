local _M = {}

local city = require("resty.libip.city")
local geo = require('resty.maxminddb')
local lfs = require("lfs")
local cjson = require("cjson.safe")

-- 数据库配置
local DB_CONFIG = {
    ipip = {
        path = "/app/data/ipip.ipdb",
        backup_path = "/app/data/ipip.ipdb.bak",
        instance = nil,
        last_modified = 0
    },
    geolite2 = {
        path = "/app/data/geolite2.mmdb",
        backup_path = "/app/data/geolite2.mmdb.bak",
        instance = nil,
        last_modified = 0
    },
    cz = {
        path = "/app/data/cz.dat",
        backup_path = "/app/data/cz.dat.bak",
        instance = nil,
        last_modified = 0
    }
}

-- 获取文件修改时间
local function get_file_mtime(filepath)
    local attr = lfs.attributes(filepath)
    return attr and attr.modification or 0
end

-- 备份数据库文件
local function backup_db_file(db_name)
    local config = DB_CONFIG[db_name]
    if not config then
        return false, "Unknown database: " .. db_name
    end
    
    local cmd = string.format("cp %s %s", config.path, config.backup_path)
    local ok = os.execute(cmd)
    return ok == 0, ok and "Backup successful" or "Backup failed"
end

-- 恢复数据库文件
local function restore_db_file(db_name)
    local config = DB_CONFIG[db_name]
    if not config then
        return false, "Unknown database: " .. db_name
    end
    
    local cmd = string.format("cp %s %s", config.backup_path, config.path)
    local ok = os.execute(cmd)
    return ok == 0, ok and "Restore successful" or "Restore failed"
end

-- 初始化数据库实例
function _M.init()
    -- 初始化 IPIP 数据库
    local ok, err = pcall(function()
        DB_CONFIG.ipip.instance = city:new(DB_CONFIG.ipip.path)
        DB_CONFIG.ipip.last_modified = get_file_mtime(DB_CONFIG.ipip.path)
        ngx.log(ngx.INFO, "IPIP database initialized successfully")
    end)
    
    if not ok then
        ngx.log(ngx.ERR, "Failed to initialize IPIP database: ", err)
    end
    
    -- 初始化 GeoLite2 数据库
    if not geo.initted() then
        local ok, err = pcall(function()
            geo.init(DB_CONFIG.geolite2.path)
            DB_CONFIG.geolite2.last_modified = get_file_mtime(DB_CONFIG.geolite2.path)
            ngx.log(ngx.INFO, "GeoLite2 database initialized successfully")
        end)
        
        if not ok then
            ngx.log(ngx.ERR, "Failed to initialize GeoLite2 database: ", err)
        end
    end
    
    -- 纯真数据库不需要预加载，每次查询时动态加载
    DB_CONFIG.cz.last_modified = get_file_mtime(DB_CONFIG.cz.path)
    
    -- 设置全局变量供其他模块使用
    ipdb = DB_CONFIG.ipip.instance
end

-- 重新加载指定数据库
function _M.reload_database(db_name)
    local config = DB_CONFIG[db_name]
    if not config then
        return false, "Unknown database: " .. db_name
    end
    
    -- 备份当前数据库
    backup_db_file(db_name)
    
    if db_name == "ipip" then
        local ok, err = pcall(function()
            local new_instance = city:new(config.path)
            config.instance = new_instance
            ipdb = new_instance  -- 更新全局变量
            config.last_modified = get_file_mtime(config.path)
        end)
        
        if not ok then
            ngx.log(ngx.ERR, "Failed to reload IPIP database: ", err)
            restore_db_file(db_name)
            return false, "Reload failed: " .. err
        end
        
    elseif db_name == "geolite2" then
        local ok, err = pcall(function()
            -- MaxMind 库需要重新初始化
            geo.init(config.path)
            config.last_modified = get_file_mtime(config.path)
        end)
        
        if not ok then
            ngx.log(ngx.ERR, "Failed to reload GeoLite2 database: ", err)
            restore_db_file(db_name)
            return false, "Reload failed: " .. err
        end
        
    elseif db_name == "cz" then
        -- 纯真数据库每次查询时动态加载，只需更新修改时间
        config.last_modified = get_file_mtime(config.path)
    end
    
    ngx.log(ngx.INFO, "Database ", db_name, " reloaded successfully")
    return true, "Reload successful"
end

-- 检查并重新加载数据库
function _M.check_and_reload(premature)
    if premature then
        return
    end
    
    for db_name, config in pairs(DB_CONFIG) do
        local current_mtime = get_file_mtime(config.path)
        if current_mtime > config.last_modified then
            ngx.log(ngx.INFO, "Detected change in ", db_name, " database, reloading...")
            local ok, err = _M.reload_database(db_name)
            if not ok then
                ngx.log(ngx.ERR, "Failed to reload ", db_name, ": ", err)
            end
        end
    end
end

-- 手动重新加载所有数据库
function _M.reload_all()
    local results = {}
    for db_name, _ in pairs(DB_CONFIG) do
        local ok, err = _M.reload_database(db_name)
        results[db_name] = {success = ok, message = err}
    end
    return results
end

-- 获取数据库状态
function _M.get_status()
    local status = {}
    for db_name, config in pairs(DB_CONFIG) do
        status[db_name] = {
            path = config.path,
            last_modified = config.last_modified,
            file_exists = lfs.attributes(config.path) ~= nil,
            backup_exists = lfs.attributes(config.backup_path) ~= nil
        }
    end
    return status
end

return _M
