local ngx               = require("ngx")
local cjson             = require("cjson.safe")
local qqwry             = require("resty.libip.qqwry")
local iconv             = require("resty.iconv")
local iso_country_code  = require("resty.libip.iso_country_code")

local IP_NOT_FOUND = '{"status":404004, "message":"IP地址找不到", "info":"定位数据库中无法找到"}'
local IP_INVALID = '{"status":4040000, "message":"查询参数无效", "info":"查询参数无效"}'


local function iconv2utf8(data)
    local from   = 'GB18030'
    local to  = 'UTF-8'

    local i, err = iconv:new(to, from)
    if not i then
        return err
    end

    local t, count = i:convert(data)
    if not t then
        return count
    end
    return t
end


local query_ip = ngx.ctx.query_ip

result = qqwry.query(query_ip)

if not result then
    ngx.status = 404
    ngx.say(cjson.encode({
        ["code"] = "19-00-4",
        ["data"] = "No record found in database."
    }))
    ngx.exit(ngx.HTTP_NOT_FOUND)
end

local response = {
    ["code"] = "0",
    ["data"] = {
        ["region"] = iconv2utf8(result[1]),
        ["isp"] = iconv2utf8(result[2]),
        ["iso_country_code"] = iso_country_code.get_iso_code(query_ip),
    },
}

ngx.say(cjson.encode(response))
