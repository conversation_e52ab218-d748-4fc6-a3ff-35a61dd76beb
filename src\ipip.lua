local ngx               = ngx
local cjson             = require("cjson.safe")
local iso_country_code  = require("resty.libip.iso_country_code")


local query_ip = ngx.ctx.query_ip

local result = ipdb:findArray(query_ip)

local response = {
    ["code"] = "0",
    ["data"] = {
        ["country"] = result[1],
        ["region"] = result[2],
        ["city"] = result[3],
        ["isp"] = "",
        ["iso_country_code"] = iso_country_code.get_iso_code(query_ip),
    },
}


ngx.say(cjson.encode(response))
