local cjson = require("cjson.safe")
local db_manager = require("resty.libip.db_manager")

-- 获取请求方法和路径
local method = ngx.var.request_method
local uri = ngx.var.uri
local args = ngx.req.get_uri_args()

-- 设置响应头
ngx.header.content_type = "application/json; charset=utf-8"

-- 路由处理
if method == "GET" and uri == "/admin/status" then
    -- 获取数据库状态
    local status = db_manager.get_status()
    ngx.say(cjson.encode({
        code = "0",
        data = status
    }))
    
elseif method == "POST" and uri == "/admin/reload" then
    -- 重新加载数据库
    local db_name = args.db
    
    if db_name then
        -- 重新加载指定数据库
        local ok, err = db_manager.reload_database(db_name)
        ngx.say(cjson.encode({
            code = ok and "0" or "1",
            data = {
                database = db_name,
                success = ok,
                message = err
            }
        }))
    else
        -- 重新加载所有数据库
        local results = db_manager.reload_all()
        ngx.say(cjson.encode({
            code = "0",
            data = results
        }))
    end
    
elseif method == "POST" and uri == "/admin/upload" then
    -- 处理数据库文件上传
    ngx.req.read_body()
    local data = ngx.req.get_body_data()
    local db_name = args.db
    
    if not db_name or not data then
        ngx.status = 400
        ngx.say(cjson.encode({
            code = "400",
            data = "Missing database name or file data"
        }))
        return
    end
    
    -- 验证数据库名称
    local valid_dbs = {ipip = true, geolite2 = true, cz = true}
    if not valid_dbs[db_name] then
        ngx.status = 400
        ngx.say(cjson.encode({
            code = "400",
            data = "Invalid database name"
        }))
        return
    end
    
    -- 写入临时文件
    local temp_path = "/tmp/" .. db_name .. "_" .. ngx.time() .. ".tmp"
    local file = io.open(temp_path, "wb")
    if not file then
        ngx.status = 500
        ngx.say(cjson.encode({
            code = "500",
            data = "Failed to create temporary file"
        }))
        return
    end
    
    file:write(data)
    file:close()
    
    -- 验证文件格式（简单检查）
    local valid = false
    if db_name == "ipip" then
        -- 检查 IPIP 数据库格式
        local test_file = io.open(temp_path, "rb")
        if test_file then
            local header = test_file:read(4)
            test_file:close()
            valid = header and #header == 4
        end
    elseif db_name == "geolite2" then
        -- 检查 MaxMind 数据库格式
        valid = true  -- 简化验证，实际应该检查 MMDB 格式
    elseif db_name == "cz" then
        -- 检查纯真数据库格式
        valid = true  -- 简化验证
    end
    
    if not valid then
        os.remove(temp_path)
        ngx.status = 400
        ngx.say(cjson.encode({
            code = "400",
            data = "Invalid database file format"
        }))
        return
    end
    
    -- 移动文件到目标位置
    local target_path = "/app/data/" .. db_name .. (db_name == "ipip" and ".ipdb" or 
                                                   db_name == "geolite2" and ".mmdb" or ".dat")
    local cmd = string.format("mv %s %s", temp_path, target_path)
    local ok = os.execute(cmd)
    
    if ok == 0 then
        -- 自动重新加载数据库
        local reload_ok, reload_err = db_manager.reload_database(db_name)
        ngx.say(cjson.encode({
            code = "0",
            data = {
                upload_success = true,
                reload_success = reload_ok,
                reload_message = reload_err
            }
        }))
    else
        ngx.status = 500
        ngx.say(cjson.encode({
            code = "500",
            data = "Failed to move uploaded file"
        }))
    end
    
else
    -- 不支持的请求
    ngx.status = 404
    ngx.say(cjson.encode({
        code = "404",
        data = "Not found"
    }))
end
