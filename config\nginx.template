daemon                          off;
worker_processes                1;

env                             ACCESS_LOG;

events {
    worker_connections          1024;
    multi_accept                on;
    use                         epoll;
}

http {
    include                     /usr/local/openresty/nginx/conf/mime.types;

    log_format                  access '$time_local|$status|sent:$body_bytes_sent|$request_method $host$request_uri|$request_body|$http_user_agent';
    access_log                  ${ACCESS_LOG};
    error_log                   /usr/local/openresty/nginx/logs/error.log error;

    sendfile                    on;
    tcp_nopush                  on;
    tcp_nodelay                 on;
    keepalive_timeout           65;
    client_max_body_size        8m;

    error_page                  404 /error?code=404;
    error_page                  500 /error?code=500;

    gzip                        on;
    gzip_vary                   on;
    gzip_comp_level             6;
    gzip_buffers                16 8k;
    gzip_min_length             1000;
    gzip_proxied                any;
    gzip_types                  text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript;
    
    init_worker_by_lua_file     /app/src/init_worker.lua;

    server {
        listen                  8080;
        default_type            'application/json;charset=utf-8';
        server_name             _;


        location = /error {
            internal;
            content_by_lua_file  /app/src/error.lua;
        }

        location = /location {
            access_by_lua_file   /app/src/access.lua;
            content_by_lua_file  /app/src/ipip.lua;
        }

        location = /cz {
            access_by_lua_file   /app/src/access.lua;
            content_by_lua_file  /app/src/cz.lua;
        }

        location = /ipv6 {
            access_by_lua_file   /app/src/access.lua;
            content_by_lua_file  /app/src/mmdb.lua;
        }

        location = /check {
            content_by_lua_file  /app/src/check.lua;
        }

        # for rancher health check
        location = /healthcheck {
            access_log off;
            content_by_lua_file  /app/src/check.lua;
        }

        location / {
            internal;
            content_by_lua_file /app/src/error.lua;
        }
    }
}