<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\Users\<USER>\.m2\repository" />
        <option name="mavenHome" value="C:\Program Files\JetBrains\IntelliJ IDEA 2020.2.3.1\plugins\maven\lib\maven3" />
        <option name="userSettingsFile" value="C:\Users\<USER>\.m2\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectViewState">
    <option name="abbreviatePackageNames" value="true" />
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
    <option name="sortByType" value="true" />
    <option name="sortKey" value="BY_TYPE" />
  </component>
  <component name="PropertiesComponent">{}</component>
</project>