local cjson     = require("cjson.safe")
local ipmatcher = require("resty.libip.ipmatcher")


local uri = ngx.var.uri

if uri == "/error"
    or uri == "/check"
then
    return
end


local function is_ipaddr(ip)
    if ipmatcher.parse_ipv4(ip) or ipmatcher.parse_ipv6(ip) then
        return true
    end

    return false
end


ngx.ctx.query_ip = ngx.unescape_uri(ngx.var.arg_ip)

if not ngx.ctx.query_ip then
    ngx.status = 400
    ngx.say(cjson.encode({
        ["code"] = "19-00-2",
        ["data"] = "Invalid request syntax."
    }))
    ngx.exit(ngx.HTTP_BAD_REQUEST)
elseif not is_ipaddr(ngx.ctx.query_ip) then
    ngx.status = 400
    ngx.say(cjson.encode({
        ["code"] = "19-00-3",
        ["data"] = "Invalid IP address format."
    }))
    ngx.exit(ngx.HTTP_BAD_REQUEST)
end
