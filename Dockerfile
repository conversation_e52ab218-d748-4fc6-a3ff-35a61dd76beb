FROM openresty/openresty:********-bionic

COPY . /app

RUN sed -i s/archive.ubuntu.com/mirrors.tuna.tsinghua.edu.cn/g /etc/apt/sources.list \
&&  sed -i s/security.ubuntu.com/mirrors.tuna.tsinghua.edu.cn/g /etc/apt/sources.list \
&&  apt update \
&&  apt install -y libmaxminddb0 libmaxminddb-dev mmdb-bin \
&&  opm install xiaooloong/lua-resty-iconv \
&&  opm install anjia0532/lua-resty-maxminddb \
&&  mkdir -p /usr/local/openresty/lualib/resty/libip \
&&  mv /app/lib/* /usr/local/openresty/lualib/resty/libip \
&& chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]

CMD ["/usr/local/openresty/bin/openresty", "-c", "/app/config/nginx.conf"]
