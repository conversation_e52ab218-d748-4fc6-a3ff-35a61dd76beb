#!/bin/bash

# IP 数据库更新脚本
# 支持从多个数据源下载和更新 IP 地址数据库

set -e

# 配置
DATA_DIR="/app/data"
BACKUP_DIR="/app/backup"
LOG_FILE="/var/log/ip-locator-update.log"
CONTAINER_NAME="ip-locator"

# 创建必要的目录
mkdir -p "$BACKUP_DIR"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 备份当前数据库
backup_database() {
    local db_file="$1"
    local backup_file="$BACKUP_DIR/$(basename $db_file).$(date +%Y%m%d_%H%M%S)"
    
    if [ -f "$db_file" ]; then
        cp "$db_file" "$backup_file"
        log "Backed up $db_file to $backup_file"
    fi
}

# 下载 IPIP 数据库
update_ipip() {
    log "Updating IPIP database..."
    
    # 备份当前数据库
    backup_database "$DATA_DIR/ipip.ipdb"
    
    # 这里需要替换为实际的 IPIP 数据库下载 URL 和认证信息
    # IPIP.net 需要付费订阅才能获取下载链接
    local download_url="YOUR_IPIP_DOWNLOAD_URL"
    local token="YOUR_IPIP_TOKEN"
    
    if [ -n "$download_url" ] && [ -n "$token" ]; then
        curl -H "Authorization: Bearer $token" \
             -o "$DATA_DIR/ipip.ipdb.tmp" \
             "$download_url"
        
        if [ $? -eq 0 ]; then
            mv "$DATA_DIR/ipip.ipdb.tmp" "$DATA_DIR/ipip.ipdb"
            log "IPIP database updated successfully"
            return 0
        else
            log "Failed to download IPIP database"
            rm -f "$DATA_DIR/ipip.ipdb.tmp"
            return 1
        fi
    else
        log "IPIP download URL or token not configured"
        return 1
    fi
}

# 下载纯真数据库
update_cz() {
    log "Updating CZ database..."
    
    # 备份当前数据库
    backup_database "$DATA_DIR/cz.dat"
    
    # 纯真数据库可以从官网免费下载
    local download_url="https://www.cz88.net/geo-public"
    
    # 下载并解压
    curl -L -o "$DATA_DIR/cz.zip" "$download_url"
    
    if [ $? -eq 0 ]; then
        cd "$DATA_DIR"
        unzip -o cz.zip
        
        # 查找解压后的 .dat 文件
        local dat_file=$(find . -name "*.dat" -type f | head -1)
        if [ -n "$dat_file" ]; then
            mv "$dat_file" "cz.dat"
            rm -f cz.zip
            log "CZ database updated successfully"
            return 0
        else
            log "No .dat file found in downloaded archive"
            return 1
        fi
    else
        log "Failed to download CZ database"
        return 1
    fi
}

# 下载 MaxMind GeoLite2 数据库
update_geolite2() {
    log "Updating GeoLite2 database..."
    
    # 备份当前数据库
    backup_database "$DATA_DIR/geolite2.mmdb"
    
    # MaxMind 需要免费注册获取 license key
    local license_key="YOUR_MAXMIND_LICENSE_KEY"
    local download_url="https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=$license_key&suffix=tar.gz"
    
    if [ -n "$license_key" ] && [ "$license_key" != "YOUR_MAXMIND_LICENSE_KEY" ]; then
        curl -o "$DATA_DIR/geolite2.tar.gz" "$download_url"
        
        if [ $? -eq 0 ]; then
            cd "$DATA_DIR"
            tar -xzf geolite2.tar.gz
            
            # 查找解压后的 .mmdb 文件
            local mmdb_file=$(find . -name "*.mmdb" -type f | head -1)
            if [ -n "$mmdb_file" ]; then
                mv "$mmdb_file" "geolite2.mmdb"
                rm -rf GeoLite2-City_* geolite2.tar.gz
                log "GeoLite2 database updated successfully"
                return 0
            else
                log "No .mmdb file found in downloaded archive"
                return 1
            fi
        else
            log "Failed to download GeoLite2 database"
            return 1
        fi
    else
        log "MaxMind license key not configured"
        return 1
    fi
}

# 通知容器重新加载数据库
reload_databases() {
    log "Reloading databases in container..."
    
    # 通过 HTTP API 重新加载数据库
    local reload_url="http://localhost:8080/admin/reload"
    
    curl -X POST "$reload_url" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "Database reload triggered successfully"
    else
        log "Failed to trigger database reload"
    fi
}

# 主函数
main() {
    log "Starting database update process..."
    
    local updated=false
    
    # 更新各个数据库
    if update_ipip; then
        updated=true
    fi
    
    if update_cz; then
        updated=true
    fi
    
    if update_geolite2; then
        updated=true
    fi
    
    # 如果有数据库更新，则重新加载
    if [ "$updated" = true ]; then
        reload_databases
        log "Database update process completed"
    else
        log "No databases were updated"
    fi
}

# 检查参数
case "${1:-all}" in
    "ipip")
        update_ipip && reload_databases
        ;;
    "cz")
        update_cz && reload_databases
        ;;
    "geolite2")
        update_geolite2 && reload_databases
        ;;
    "all")
        main
        ;;
    *)
        echo "Usage: $0 [ipip|cz|geolite2|all]"
        exit 1
        ;;
esac
