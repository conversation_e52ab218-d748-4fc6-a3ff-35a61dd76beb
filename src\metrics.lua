local cjson = require("cjson.safe")
local db_manager = require("resty.libip.db_manager")

-- Prometheus 格式的指标输出
local function prometheus_metrics()
    local status = db_manager.get_status()
    local metrics = {}
    
    -- 数据库状态指标
    for db_name, db_status in pairs(status) do
        local file_exists = db_status.file_exists and 1 or 0
        local backup_exists = db_status.backup_exists and 1 or 0
        
        table.insert(metrics, string.format(
            'ip_database_file_exists{database="%s"} %d', 
            db_name, file_exists
        ))
        
        table.insert(metrics, string.format(
            'ip_database_backup_exists{database="%s"} %d', 
            db_name, backup_exists
        ))
        
        table.insert(metrics, string.format(
            'ip_database_last_modified{database="%s"} %d', 
            db_name, db_status.last_modified
        ))
    end
    
    -- 服务运行时间
    table.insert(metrics, string.format(
        'ip_locator_uptime_seconds %d', 
        ngx.time() - (ngx.shared.stats and ngx.shared.stats:get("start_time") or ngx.time())
    ))
    
    -- 请求计数器（如果有的话）
    if ngx.shared.stats then
        local total_requests = ngx.shared.stats:get("total_requests") or 0
        local ipip_requests = ngx.shared.stats:get("ipip_requests") or 0
        local cz_requests = ngx.shared.stats:get("cz_requests") or 0
        local mmdb_requests = ngx.shared.stats:get("mmdb_requests") or 0
        
        table.insert(metrics, string.format('ip_locator_requests_total %d', total_requests))
        table.insert(metrics, string.format('ip_locator_requests_total{database="ipip"} %d', ipip_requests))
        table.insert(metrics, string.format('ip_locator_requests_total{database="cz"} %d', cz_requests))
        table.insert(metrics, string.format('ip_locator_requests_total{database="mmdb"} %d', mmdb_requests))
    end
    
    return table.concat(metrics, '\n') .. '\n'
end

-- JSON 格式的指标输出
local function json_metrics()
    local status = db_manager.get_status()
    local start_time = ngx.shared.stats and ngx.shared.stats:get("start_time") or ngx.time()
    
    local metrics = {
        timestamp = ngx.time(),
        uptime_seconds = ngx.time() - start_time,
        databases = status,
        requests = {}
    }
    
    if ngx.shared.stats then
        metrics.requests = {
            total = ngx.shared.stats:get("total_requests") or 0,
            ipip = ngx.shared.stats:get("ipip_requests") or 0,
            cz = ngx.shared.stats:get("cz_requests") or 0,
            mmdb = ngx.shared.stats:get("mmdb_requests") or 0
        }
    end
    
    return cjson.encode(metrics)
end

-- 根据 Accept 头返回不同格式
local accept_header = ngx.var.http_accept or ""

if string.find(accept_header, "application/json") then
    ngx.header.content_type = "application/json"
    ngx.say(json_metrics())
else
    ngx.header.content_type = "text/plain"
    ngx.say(prometheus_metrics())
end
