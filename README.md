# lua-resty-ip-locator
IP 地址归属地查询服务。
* IPIP 数据库版本（https://www.ipip.net/product/ip.html ）：2020-07-03
* 纯真数据库版本 (https://www.cz88.net/geo-public ) ：2022-04-20
* Maxmind GeoLite 2 (IPv6)：2022-05-17

## 启动
```bash
docker run -d --name ip-locator -p 8080:8080 ip-locator:0.3.0
```

如需关闭访问日志，则增加环境变量 `ACCESS_LOG=off`。

## 接口
使用 IPIP 数据库查询 IP 归属地：
```
GET /location?ip={query_ip}

{"data":{"country":"中国","isp":"","region":"广东","city":"深圳","iso_country_code":"CN"},"code":"0"}
```


使用纯真数据库查询 IP 归属地：
```
GET /cz?ip=**************

{"data":{"region":"广东省深圳市","isp":"电信","iso_country_code":"CN"},"code":"0"}
```
