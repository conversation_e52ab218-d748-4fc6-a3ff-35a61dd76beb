local cjson = require 'cjson'
local geo = require 'resty.maxminddb'


if not geo.initted() then
    geo.init("/app/data/geolite2.mmdb")
end


local query_ip = ngx.ctx.query_ip

local result, err = geo.lookup(query_ip)

if not result then
    ngx.status = 404
    ngx.say(cjson.encode({
        ["code"] = "19-00-4",
        ["data"] = "No record found in database.",
    }))
    ngx.exit(ngx.HTTP_NOT_FOUND)
end


local response = {
    ["code"] = "0",
    ["data"] = {
        ["continent"] = result["continent"]["names"]["zh-CN"],
        ["country"] = result["country"]["names"]["zh-CN"],
        ["region"] = "",
        ["city"] = "",
        ["isp"] = "",
        ["iso_country_code"] = result["country"]["iso_code"],
    },
}


ngx.say(cjson.encode(response))
