# IP 数据库实时更新解决方案

## 概述

本项目提供了多种 IP 数据库实时更新的解决方案，支持 IPIP、纯真数据库和 MaxMind GeoLite2 三种数据源的自动更新。

## 方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 热重载机制 | 零停机时间，实时生效 | 需要修改代码 | 生产环境推荐 |
| 外部更新脚本 | 简单易用，灵活配置 | 需要外部调度 | 中小型部署 |
| Kubernetes CronJob | 云原生，自动化程度高 | 需要 K8s 环境 | 大规模云部署 |
| Docker Compose | 一键部署，包含监控 | 资源消耗较大 | 开发测试环境 |

## 快速开始

### 1. 热重载机制部署

```bash
# 构建镜像
docker build -t ip-locator:latest .

# 启动服务
docker run -d --name ip-locator \
  -p 8080:8080 \
  -v $(pwd)/data:/app/data \
  ip-locator:latest
```

### 2. 管理接口使用

#### 查看数据库状态
```bash
curl http://localhost:8080/admin/status
```

#### 手动重新加载数据库
```bash
# 重新加载所有数据库
curl -X POST http://localhost:8080/admin/reload

# 重新加载指定数据库
curl -X POST "http://localhost:8080/admin/reload?db=ipip"
```

#### 上传新数据库文件
```bash
# 上传 IPIP 数据库
curl -X POST \
  -H "Content-Type: application/octet-stream" \
  --data-binary @new_ipip.ipdb \
  "http://localhost:8080/admin/upload?db=ipip"
```

### 3. 使用 Docker Compose

```bash
# 设置环境变量
export MAXMIND_LICENSE_KEY="your_license_key"
export IPIP_TOKEN="your_ipip_token"

# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f database-updater
```

### 4. Kubernetes 部署

```bash
# 创建密钥
kubectl create secret generic ip-database-secrets \
  --from-literal=maxmind-license-key="your_license_key" \
  --from-literal=ipip-token="your_ipip_token"

# 部署 CronJob
kubectl apply -f k8s/database-updater.yaml
```

## 配置说明

### 数据源配置

#### MaxMind GeoLite2
1. 注册 MaxMind 账户：https://www.maxmind.com/en/geolite2/signup
2. 获取 License Key
3. 设置环境变量 `MAXMIND_LICENSE_KEY`

#### IPIP 数据库
1. 购买 IPIP.net 服务：https://www.ipip.net/
2. 获取 API Token
3. 设置环境变量 `IPIP_TOKEN`

#### 纯真数据库
- 免费下载，无需配置

### 更新频率配置

#### 定时任务配置
```bash
# 编辑 crontab
crontab -e

# 每天凌晨 2 点更新
0 2 * * * /path/to/update_databases.sh

# 每周一更新
0 2 * * 1 /path/to/update_databases.sh
```

#### Kubernetes CronJob
```yaml
spec:
  # 每天凌晨 2 点
  schedule: "0 2 * * *"
  
  # 每周一凌晨 2 点
  schedule: "0 2 * * 1"
  
  # 每小时
  schedule: "0 * * * *"
```

## 监控和告警

### Prometheus 指标

访问 `http://localhost:8080/metrics` 获取 Prometheus 格式的指标：

```
ip_database_file_exists{database="ipip"} 1
ip_database_backup_exists{database="ipip"} 1
ip_database_last_modified{database="ipip"} **********
ip_locator_uptime_seconds 86400
ip_locator_requests_total 12345
```

### Grafana 仪表板

1. 访问 http://localhost:3000
2. 用户名/密码：admin/admin
3. 导入预配置的仪表板

### 告警规则示例

```yaml
groups:
- name: ip-locator
  rules:
  - alert: DatabaseFileNotFound
    expr: ip_database_file_exists == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "IP database file not found"
      description: "Database {{ $labels.database }} file is missing"

  - alert: DatabaseNotUpdated
    expr: (time() - ip_database_last_modified) > 86400 * 7
    for: 1h
    labels:
      severity: warning
    annotations:
      summary: "IP database not updated for a week"
      description: "Database {{ $labels.database }} hasn't been updated for over a week"
```

## 故障排除

### 常见问题

1. **数据库加载失败**
   - 检查文件权限
   - 验证文件格式
   - 查看错误日志

2. **更新脚本失败**
   - 检查网络连接
   - 验证 API 密钥
   - 检查磁盘空间

3. **热重载不生效**
   - 检查文件修改时间
   - 验证定时器是否启动
   - 查看 Nginx 错误日志

### 日志查看

```bash
# Docker 容器日志
docker logs ip-locator

# Nginx 错误日志
docker exec ip-locator tail -f /usr/local/openresty/nginx/logs/error.log

# 更新脚本日志
tail -f /var/log/ip-locator-update.log
```

## 性能优化

### 内存优化
- 使用 `lua_shared_dict` 缓存常用查询结果
- 定期清理过期缓存

### 并发优化
- 调整 `worker_processes` 数量
- 优化数据库查询算法

### 网络优化
- 启用 gzip 压缩
- 使用 CDN 分发服务

## 安全考虑

### 管理接口安全
```nginx
location ~ ^/admin/ {
    allow 127.0.0.1;
    allow 10.0.0.0/8;
    deny all;
    
    auth_basic "Admin Area";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    content_by_lua_file /app/src/admin.lua;
}
```

### 数据传输安全
- 使用 HTTPS
- 验证上传文件格式
- 限制上传文件大小

## 扩展功能

### 自定义数据源
1. 实现数据源适配器
2. 添加到 `db_manager.lua`
3. 更新配置文件

### 多实例部署
- 使用负载均衡器
- 共享存储卷
- 数据库同步机制
